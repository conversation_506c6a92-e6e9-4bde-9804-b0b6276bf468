#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试间距分析日志功能
"""

import numpy as np

# 创建模拟的间距分析数据
mock_spacing_data = [
    {
        'Offset1_mm': 0.0,
        'Offset2_mm': 0.42,
        'ProjectedStep_mm': 0.42,
        'Target3DSpacing_mm': 0.42,
        'ActualAvg3DSpacing_mm': 0.415,
        'Error_mm': -0.005,
        'Min3DSpacing_mm': 0.38,
        'Max3DSpacing_mm': 0.45,
        'Median3DSpacing_mm': 0.41,
        'StdDev3DSpacing_mm': 0.025,
        'NumSamples': 15
    },
    {
        'Offset1_mm': 0.42,
        'Offset2_mm': 0.84,
        'ProjectedStep_mm': 0.42,
        'Target3DSpacing_mm': 0.42,
        'ActualAvg3DSpacing_mm': 0.438,
        'Error_mm': 0.018,
        'Min3DSpacing_mm': 0.39,
        'Max3DSpacing_mm': 0.48,
        'Median3DSpacing_mm': 0.435,
        'StdDev3DSpacing_mm': 0.032,
        'NumSamples': 18
    },
    {
        'Offset1_mm': 0.84,
        'Offset2_mm': 1.26,
        'ProjectedStep_mm': 0.42,
        'Target3DSpacing_mm': 0.42,
        'ActualAvg3DSpacing_mm': 0.402,
        'Error_mm': -0.018,
        'Min3DSpacing_mm': 0.35,
        'Max3DSpacing_mm': 0.46,
        'Median3DSpacing_mm': 0.405,
        'StdDev3DSpacing_mm': 0.028,
        'NumSamples': 12
    }
]

def test_log_function():
    """测试日志功能的核心逻辑"""
    print("🧪 测试间距分析日志功能的核心逻辑...")
    
    # 提取有效的间距数据
    valid_spacings = []
    total_samples = 0
    for data in mock_spacing_data:
        if 'ActualAvg3DSpacing_mm' in data and not np.isnan(float(data['ActualAvg3DSpacing_mm'])):
            valid_spacings.append(float(data['ActualAvg3DSpacing_mm']))
            if 'NumSamples' in data:
                total_samples += int(data['NumSamples'])
    
    if not valid_spacings:
        print("❌ 无有效间距数据可分析")
        return False
        
    valid_spacings = np.array(valid_spacings)
    target_spacing = 0.42
    
    print("\n" + "="*80)
    print("                    全面间距分析报告")
    print("="*80)
    
    # 1. 基础统计信息
    print(f"\n📊 间距统计信息:")
    print(f"   目标间距:     {target_spacing:.3f} mm")
    print(f"   实际平均:     {np.mean(valid_spacings):.3f} mm")
    print(f"   实际中位数:   {np.median(valid_spacings):.3f} mm")
    print(f"   标准差:       {np.std(valid_spacings):.3f} mm")
    print(f"   最小值:       {np.min(valid_spacings):.3f} mm")
    print(f"   最大值:       {np.max(valid_spacings):.3f} mm")
    print(f"   变化范围:     {np.max(valid_spacings) - np.min(valid_spacings):.3f} mm")
    print(f"   路径条带对数: {len(valid_spacings)}")
    print(f"   总采样点数:   {total_samples}")
    
    # 2. 达成率分析
    print(f"\n🎯 间距达成率分析:")
    tolerances = [0.05, 0.08, 0.10, 0.15]  # ±5%, ±8%, ±10%, ±15%
    for tol in tolerances:
        within_tolerance = np.abs(valid_spacings - target_spacing) <= (target_spacing * tol)
        achievement_rate = np.sum(within_tolerance) / len(valid_spacings) * 100
        status = "✅" if achievement_rate >= 85 else "⚠️" if achievement_rate >= 70 else "❌"
        print(f"   ±{tol*100:2.0f}% 容差: {achievement_rate:5.1f}% {status}")
    
    # 3. RMS误差和质量指标
    rms_error = np.sqrt(np.mean((valid_spacings - target_spacing) ** 2))
    rms_error_percentage = (rms_error / target_spacing) * 100
    
    print(f"\n📈 质量指标:")
    print(f"   RMS误差:      {rms_error:.4f} mm ({rms_error_percentage:.1f}%)")
    
    # 偏差分析
    mean_deviation = np.mean(valid_spacings) - target_spacing
    mean_deviation_pct = (mean_deviation / target_spacing) * 100
    print(f"   平均偏差:     {mean_deviation:+.4f} mm ({mean_deviation_pct:+.1f}%)")
    
    # 一致性分析
    cv = np.std(valid_spacings) / np.mean(valid_spacings) * 100  # 变异系数
    print(f"   变异系数:     {cv:.1f}%")
    
    # 4. 性能指标
    print(f"\n⚡ 性能指标:")
    print(f"   运行时间:     5.23 秒")
    print(f"   缓存命中率:   75.2%")
    print(f"   总计算次数:   156")
    print(f"   收敛次数:     12")
    print(f"   间距警告:     3 次")
    
    # 5. 分布分析
    print(f"\n📊 间距分布分析:")
    q25, q75 = np.percentile(valid_spacings, [25, 75])
    iqr = q75 - q25
    print(f"   第一四分位数: {q25:.3f} mm")
    print(f"   第三四分位数: {q75:.3f} mm")
    print(f"   四分位距:     {iqr:.3f} mm")
    
    # 异常值检测
    lower_bound = q25 - 1.5 * iqr
    upper_bound = q75 + 1.5 * iqr
    outliers = valid_spacings[(valid_spacings < lower_bound) | (valid_spacings > upper_bound)]
    print(f"   异常值数量:   {len(outliers)} ({len(outliers)/len(valid_spacings)*100:.1f}%)")
    
    # 6. 综合质量评分
    primary_tolerance = 0.05  # ±5%主要容差
    primary_achievement = np.sum(np.abs(valid_spacings - target_spacing) <= (target_spacing * primary_tolerance)) / len(valid_spacings) * 100
    
    # 模拟质量评分
    quality_score = 82.5
    
    print(f"\n🏆 综合质量评分:")
    print(f"   总体评分:     {quality_score:.1f}/100")
    
    if quality_score >= 90:
        grade = "A+ (优秀)"
        emoji = "🌟"
    elif quality_score >= 85:
        grade = "A  (良好)"
        emoji = "✨"
    elif quality_score >= 80:
        grade = "B+ (较好)"
        emoji = "👍"
    elif quality_score >= 75:
        grade = "B  (一般)"
        emoji = "👌"
    elif quality_score >= 70:
        grade = "C+ (及格)"
        emoji = "⚠️"
    else:
        grade = "C- (需改进)"
        emoji = "❌"
        
    print(f"   质量等级:     {grade} {emoji}")
    print(f"   改进建议:     良好：间距控制较好，可考虑微调参数")
    
    # 7. 目标达成总结
    print(f"\n📋 目标达成总结:")
    print(f"   主要目标 (±5%):  {primary_achievement:.1f}% {'✅达成' if primary_achievement >= 85 else '❌未达成'}")
    print(f"   RMS误差目标:     {rms_error_percentage:.1f}% {'✅达成' if rms_error_percentage <= 15 else '❌未达成'} (目标: ≤15%)")
    print(f"   间距警告目标:    3 次 {'✅达成' if 3 <= 10 else '❌未达成'} (目标: ≤10次)")
    print(f"   质量评分目标:    {quality_score:.1f} {'✅达成' if quality_score >= 80 else '❌未达成'} (目标: ≥80分)")
    
    print("="*80)
    
    return True

if __name__ == "__main__":
    print("🚀 测试间距分析日志功能的核心逻辑")
    print("="*60)
    
    if test_log_function():
        print("\n🎉 测试成功！日志功能逻辑正常工作。")
    else:
        print("\n❌ 测试失败。")
    
    print("="*60)
