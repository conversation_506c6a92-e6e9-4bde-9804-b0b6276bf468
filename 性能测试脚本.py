#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
非平面切片器性能测试脚本
用于验证优化后的性能提升效果
"""

import time
import os
import sys

def test_performance():
    """测试优化后的性能"""
    print("🚀 开始性能测试...")
    print("="*60)
    
    # 检查是否存在测试用的STL文件
    test_files = []
    for file in os.listdir('.'):
        if file.endswith('.stl'):
            test_files.append(file)
    
    if not test_files:
        print("❌ 未找到STL测试文件")
        print("请确保当前目录下有STL文件用于测试")
        return False
    
    print(f"📁 找到 {len(test_files)} 个STL文件:")
    for i, file in enumerate(test_files[:3], 1):  # 最多测试3个文件
        print(f"   {i}. {file}")
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        # 导入并运行nonplanar_slicer
        print(f"\n⏱️  开始运行优化后的nonplanar_slicer...")
        
        # 这里应该调用主函数，但由于文件名包含空格，我们使用subprocess
        import subprocess
        result = subprocess.run([
            sys.executable, 
            "nonplanar_slicer 0528.py"
        ], capture_output=True, text=True, timeout=60)
        
        # 记录结束时间
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"✅ 运行完成!")
        print(f"⏱️  总运行时间: {total_time:.2f} 秒")
        
        # 分析性能
        if total_time <= 4.0:
            performance_grade = "🌟 优秀"
            improvement = ((7.15 - total_time) / 7.15) * 100
            print(f"🎯 性能提升: {improvement:.1f}% (目标: 40-50%)")
        elif total_time <= 5.0:
            performance_grade = "✨ 良好"
            improvement = ((7.15 - total_time) / 7.15) * 100
            print(f"🎯 性能提升: {improvement:.1f}% (接近目标)")
        else:
            performance_grade = "⚠️ 需要进一步优化"
            improvement = ((7.15 - total_time) / 7.15) * 100
            print(f"🎯 性能提升: {improvement:.1f}% (未达到目标)")
        
        print(f"📊 性能等级: {performance_grade}")
        
        # 检查输出中的性能统计
        output = result.stdout
        if "缓存命中率" in output:
            print(f"\n📈 从输出中提取的性能指标:")
            lines = output.split('\n')
            for line in lines:
                if any(keyword in line for keyword in ["缓存命中率", "总迭代次数", "收敛率", "运行时间"]):
                    print(f"   {line.strip()}")
        
        # 检查是否有间距分析报告
        if "全面间距分析报告" in output:
            print(f"✅ 间距分析日志功能正常工作")
        else:
            print(f"⚠️ 未检测到间距分析日志输出")
        
        return True
        
    except subprocess.TimeoutExpired:
        print(f"❌ 运行超时 (>60秒)")
        return False
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        return False

def analyze_optimization_impact():
    """分析优化影响"""
    print(f"\n📊 优化影响分析:")
    print(f"="*60)
    
    optimizations = [
        ("迭代次数减少", "15→8次", "47%"),
        ("采样点减少", "10→6个", "40%"),
        ("缓存容量增加", "10K→15K", "50%"),
        ("容差放宽", "0.01→0.015mm", "33%"),
        ("智能初始猜测", "简单→加权平均", "10-15%"),
        ("早期停止优化", "严格→放宽标准", "20%")
    ]
    
    print(f"🔧 实施的优化措施:")
    for i, (name, change, impact) in enumerate(optimizations, 1):
        print(f"   {i}. {name}: {change} (预期影响: {impact})")
    
    print(f"\n🎯 预期总体性能提升: 43-51%")
    print(f"🎯 目标运行时间: 3.5-4.0秒 (从7.15秒)")

def check_quality_metrics():
    """检查质量指标"""
    print(f"\n🎯 质量指标检查:")
    print(f"="*60)
    
    targets = [
        ("间距达成率", "≥85%", "±5%容差内"),
        ("RMS误差", "<15%", "相对于目标间距"),
        ("质量评分", ">80分", "综合质量评分"),
        ("间距警告", "<10次", "每次运行"),
        ("核心计算", "不变", "path_row_spacing = target_bead_width * 0.7")
    ]
    
    print(f"📋 需要满足的质量目标:")
    for i, (metric, target, description) in enumerate(targets, 1):
        print(f"   {i}. {metric}: {target} ({description})")
    
    print(f"\n✅ 优化策略确保:")
    print(f"   • 保持核心算法逻辑不变")
    print(f"   • 维持间距分析日志功能")
    print(f"   • 保留所有质量监控机制")

def main():
    """主函数"""
    print("🧪 非平面切片器性能优化验证")
    print("="*60)
    print("目标: 将运行时间从7.15秒减少到3-4秒 (40-50%提升)")
    print("="*60)
    
    # 分析优化影响
    analyze_optimization_impact()
    
    # 检查质量指标
    check_quality_metrics()
    
    # 运行性能测试
    print(f"\n" + "="*60)
    success = test_performance()
    
    print(f"\n" + "="*60)
    if success:
        print("🎉 性能测试完成!")
        print("📝 请检查上述输出中的性能指标和质量数据")
        print("📊 如果运行时间在3.5-4.0秒范围内，说明优化成功")
    else:
        print("❌ 性能测试失败")
        print("🔧 可能需要进一步调整优化参数")
    
    print("="*60)

if __name__ == "__main__":
    main()
